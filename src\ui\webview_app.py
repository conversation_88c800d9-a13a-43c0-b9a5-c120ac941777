# -*- coding: utf-8 -*-
"""
PyWebview桌面应用
使用PyWebview创建跨平台的桌面应用，提供交易系统的用户界面

Author: SuperBot Team
Date: 2025-01-05
"""

import os
import sys
import threading
import time
from pathlib import Path
from typing import Dict, Any, Optional

import webview
from webview.window import Window

from src.ui.api import WebviewAPI
from src.utils.logger import get_logger
from src.utils.config import get_config_manager

logger = get_logger(__name__)


class SuperBotWebviewApp:
    """SuperBot PyWebview桌面应用"""
    
    def __init__(self):
        """初始化应用"""
        self.window: Optional[Window] = None
        self.api = WebviewAPI()
        self.config_manager = get_config_manager()
        
        # 应用配置
        self.app_config = {
            'title': 'SuperBot - 加密货币量化交易系统',
            'width': 1200,
            'height': 800,
            'min_width': 800,
            'min_height': 600,
            'resizable': True,
            'fullscreen': False,
            'minimized': False,
            'on_top': False,
            'shadow': True,
            'debug': False
        }
        
        # 窗口状态
        self.window_state = {
            'is_running': False,
            'is_minimized': False,
            'is_maximized': False,
            'position': {'x': 100, 'y': 100},
            'size': {'width': 1200, 'height': 800}
        }
        
        # 加载配置
        self._load_app_config()
        
        logger.info("PyWebview应用初始化完成")
    
    def _load_app_config(self):
        """加载应用配置"""
        try:
            # 从配置管理器加载窗口配置
            window_config = self.config_manager.get('ui.window', {})

            if window_config and isinstance(window_config, dict):
                self.app_config.update(window_config)
                logger.info("应用配置加载成功")
            else:
                logger.info("使用默认应用配置")

        except Exception as e:
            logger.error(f"加载应用配置失败: {e}")
            logger.info("使用默认应用配置")
    
    def _save_app_config(self):
        """保存应用配置"""
        try:
            # 更新窗口状态到配置
            if self.window:
                # 获取当前窗口状态
                self.window_state['size']['width'] = self.app_config['width']
                self.window_state['size']['height'] = self.app_config['height']
            
            # 保存到配置管理器
            self.config_manager.set('ui.window', self.app_config)
            self.config_manager.set('ui.window_state', self.window_state)
            
            logger.info("应用配置保存成功")
            
        except Exception as e:
            logger.error(f"保存应用配置失败: {e}")
    
    def _get_static_path(self) -> str:
        """获取静态资源路径"""
        try:
            # 获取当前文件的目录
            current_dir = Path(__file__).parent
            static_dir = current_dir / 'static'
            
            # 确保静态资源目录存在
            if not static_dir.exists():
                static_dir.mkdir(parents=True, exist_ok=True)
                logger.warning(f"静态资源目录不存在，已创建: {static_dir}")
            
            return str(static_dir)
            
        except Exception as e:
            logger.error(f"获取静态资源路径失败: {e}")
            return str(Path(__file__).parent / 'static')
    
    def _on_window_loaded(self):
        """窗口加载完成回调"""
        try:
            logger.info("应用窗口加载完成")
            self.window_state['is_running'] = True
            
            # 初始化API
            self.api.initialize()
            
        except Exception as e:
            logger.error(f"窗口加载完成回调失败: {e}")
    
    def _on_window_closing(self):
        """窗口关闭回调"""
        try:
            logger.info("应用窗口正在关闭")
            
            # 保存配置
            self._save_app_config()
            
            # 清理资源
            self.api.cleanup()
            
            self.window_state['is_running'] = False
            
        except Exception as e:
            logger.error(f"窗口关闭回调失败: {e}")
    
    def create_window(self) -> Window:
        """创建应用窗口"""
        try:
            # 获取静态资源路径
            static_path = self._get_static_path()
            
            # 创建窗口
            self.window = webview.create_window(
                title=self.app_config['title'],
                url=static_path,
                js_api=self.api,
                width=self.app_config['width'],
                height=self.app_config['height'],
                min_size=(self.app_config['min_width'], self.app_config['min_height']),
                resizable=self.app_config['resizable'],
                fullscreen=self.app_config['fullscreen'],
                minimized=self.app_config['minimized'],
                on_top=self.app_config['on_top'],
                shadow=self.app_config['shadow']
            )
            
            logger.info(f"应用窗口创建成功: {self.app_config['title']}")
            return self.window
            
        except Exception as e:
            logger.error(f"创建应用窗口失败: {e}")
            raise
    
    def start(self, debug: bool = False):
        """启动应用"""
        try:
            logger.info("启动PyWebview应用")
            
            # 设置调试模式
            self.app_config['debug'] = debug
            
            # 创建窗口
            self.create_window()
            
            # 启动webview
            webview.start(
                debug=debug,
                http_server=True,
                private_mode=False
            )
            
        except Exception as e:
            logger.error(f"启动应用失败: {e}")
            raise
        finally:
            # 清理资源
            self._on_window_closing()
    
    def stop(self):
        """停止应用"""
        try:
            logger.info("停止PyWebview应用")
            
            if self.window:
                self.window.destroy()
            
            self.window_state['is_running'] = False
            
        except Exception as e:
            logger.error(f"停止应用失败: {e}")
    
    def restart(self):
        """重启应用"""
        try:
            logger.info("重启PyWebview应用")
            
            # 停止当前应用
            self.stop()
            
            # 等待一段时间
            time.sleep(1)
            
            # 重新启动
            self.start()
            
        except Exception as e:
            logger.error(f"重启应用失败: {e}")
    
    def get_window_state(self) -> Dict[str, Any]:
        """获取窗口状态"""
        return self.window_state.copy()
    
    def set_window_config(self, config: Dict[str, Any]):
        """设置窗口配置"""
        try:
            self.app_config.update(config)
            self._save_app_config()
            logger.info("窗口配置更新成功")
            
        except Exception as e:
            logger.error(f"设置窗口配置失败: {e}")


def main():
    """主函数"""
    try:
        # 创建应用实例
        app = SuperBotWebviewApp()
        
        # 启动应用
        app.start(debug=True)
        
    except KeyboardInterrupt:
        logger.info("用户中断应用")
    except Exception as e:
        logger.error(f"应用运行异常: {e}")
    finally:
        logger.info("应用已退出")


if __name__ == "__main__":
    main()
