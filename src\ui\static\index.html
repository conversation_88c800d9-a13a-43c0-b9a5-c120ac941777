<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SuperBot - 加密货币量化交易系统</title>
    
    <!-- Ant Design CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/antd@5.21.6/dist/reset.css">
    
    <!-- React 18 -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    
    <!-- Ant Design -->
    <script src="https://cdn.jsdelivr.net/npm/antd@5.21.6/dist/antd.min.js"></script>
    
    <!-- Babel Standalone for JSX -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <!-- Day.js for date handling -->
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/dayjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/plugin/relativeTime.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.10/locale/zh-cn.js"></script>
    
    <!-- Chart.js for charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    
    <!-- Custom styles -->
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .app-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .app-header {
            background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
            color: white;
            padding: 0 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 64px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .app-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 20px;
            font-weight: 600;
        }
        
        .app-content {
            flex: 1;
            display: flex;
            overflow: hidden;
        }
        
        .app-sidebar {
            width: 240px;
            background: white;
            border-right: 1px solid #f0f0f0;
        }
        
        .app-main {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
            background: #f5f5f5;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #52c41a;
            animation: pulse 2s infinite;
        }
        
        .status-dot.warning {
            background: #faad14;
        }
        
        .status-dot.error {
            background: #ff4d4f;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .metric-value.positive {
            color: #52c41a;
        }
        
        .metric-value.negative {
            color: #ff4d4f;
        }
        
        .metric-change.positive {
            color: #52c41a;
        }
        
        .metric-change.negative {
            color: #ff4d4f;
        }
        
        .env-switch {
            display: flex;
            align-items: center;
            gap: 12px;
            color: white;
        }
        
        .connection-status {
            display: flex;
            align-items: center;
            gap: 8px;
            color: white;
        }
    </style>
</head>
<body>
    <div id="root"></div>
    
    <script type="text/babel">
        const { useState, useEffect, useCallback } = React;
        const { 
            Layout, Menu, Card, Row, Col, Statistic, Table, Button, Switch, 
            Tag, Space, Typography, Divider, Alert, Spin, Progress, Badge,
            Form, Input, InputNumber, Select, Modal, message, Tooltip,
            Tabs, List, Avatar, Timeline, Drawer, Popconfirm
        } = antd;
        
        const { Header, Sider, Content } = Layout;
        const { Title, Text } = Typography;
        const { Option } = Select;
        
        // 配置dayjs
        dayjs.extend(dayjs_plugin_relativeTime);
        dayjs.locale('zh-cn');
        
        // 主应用组件
        function App() {
            const [currentPage, setCurrentPage] = useState('dashboard');
            const [isTrading, setIsTrading] = useState(false);
            const [isLiveMode, setIsLiveMode] = useState(false);
            const [connectionStatus, setConnectionStatus] = useState('connected');
            const [loading, setLoading] = useState(false);
            
            // 系统状态数据
            const [systemData, setSystemData] = useState({
                totalBalance: 130839.39,
                availableBalance: 115628.49,
                usedMargin: 15210.90,
                marginRatio: 11.63,
                todayPnl: 1234.56,
                todayPnlPercent: 0.94,
                winRate: 68,
                totalTrades: 24,
                avgProfit: 51.23,
                maxDrawdown: -3.2,
                positions: [],
                tradingPairs: ['BTC/USDT', 'ETH/USDT', 'BNB/USDT'],
                activePairs: ['BTC/USDT', 'ETH/USDT']
            });
            
            // API调用函数
            const callAPI = useCallback(async (method, ...args) => {
                try {
                    if (window.pywebview && window.pywebview.api) {
                        return await window.pywebview.api[method](...args);
                    } else {
                        console.warn('PyWebview API not available, using mock data');
                        return null;
                    }
                } catch (error) {
                    console.error(`API call failed: ${method}`, error);
                    message.error(`API调用失败: ${error.message}`);
                    return null;
                }
            }, []);
            
            // 刷新数据
            const refreshData = useCallback(async () => {
                setLoading(true);
                try {
                    const [status, positions, balance] = await Promise.all([
                        callAPI('get_system_status'),
                        callAPI('get_positions'),
                        callAPI('get_account_balance')
                    ]);
                    
                    if (status) {
                        setIsTrading(status.trading_active);
                        setConnectionStatus(status.connection_status || 'connected');
                    }
                    
                    if (positions) {
                        setSystemData(prev => ({ ...prev, positions }));
                    }
                    
                    if (balance) {
                        setSystemData(prev => ({ 
                            ...prev, 
                            totalBalance: balance.total,
                            availableBalance: balance.available,
                            usedMargin: balance.used_margin
                        }));
                    }
                } catch (error) {
                    console.error('Failed to refresh data:', error);
                } finally {
                    setLoading(false);
                }
            }, [callAPI]);
            
            // 组件挂载时初始化
            useEffect(() => {
                refreshData();
                const interval = setInterval(refreshData, 5000); // 每5秒刷新一次
                return () => clearInterval(interval);
            }, [refreshData]);
            
            // 菜单项配置
            const menuItems = [
                { key: 'dashboard', icon: '📊', label: '仪表盘' },
                { key: 'trading', icon: '📈', label: '交易控制' },
                { key: 'positions', icon: '💼', label: '持仓管理' },
                { key: 'config', icon: '⚙️', label: '系统配置' },
                { key: 'logs', icon: '📋', label: '日志记录' }
            ];
            
            return (
                <Layout className="app-container">
                    <Header className="app-header">
                        <div className="app-logo">
                            <span>🤖</span>
                            <span>SuperBot</span>
                            <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: '14px', marginLeft: '8px' }}>
                                量化交易系统
                            </Text>
                        </div>
                        
                        <Space size="large">
                            <div className="env-switch">
                                <Text style={{ color: 'white' }}>模拟</Text>
                                <Switch 
                                    checked={isLiveMode}
                                    onChange={setIsLiveMode}
                                    size="small"
                                />
                                <Text style={{ color: 'white' }}>实盘</Text>
                            </div>
                            
                            <div className="connection-status">
                                <div className={`status-dot ${connectionStatus === 'connected' ? '' : 'error'}`}></div>
                                <Text style={{ color: 'white' }}>
                                    {connectionStatus === 'connected' ? '已连接' : '连接异常'}
                                </Text>
                            </div>
                        </Space>
                    </Header>
                    
                    <Layout className="app-content">
                        <Sider className="app-sidebar" theme="light">
                            <Menu
                                mode="inline"
                                selectedKeys={[currentPage]}
                                style={{ height: '100%', borderRight: 0 }}
                                onSelect={({ key }) => setCurrentPage(key)}
                            >
                                {menuItems.map(item => (
                                    <Menu.Item key={item.key} icon={<span>{item.icon}</span>}>
                                        {item.label}
                                    </Menu.Item>
                                ))}
                            </Menu>
                        </Sider>
                        
                        <Content className="app-main">
                            {currentPage === 'dashboard' && <DashboardPage systemData={systemData} loading={loading} refreshData={refreshData} />}
                            {currentPage === 'trading' && <TradingPage systemData={systemData} isTrading={isTrading} setIsTrading={setIsTrading} callAPI={callAPI} />}
                            {currentPage === 'positions' && <PositionsPage systemData={systemData} callAPI={callAPI} />}
                            {currentPage === 'config' && <ConfigPage callAPI={callAPI} />}
                            {currentPage === 'logs' && <LogsPage callAPI={callAPI} />}
                        </Content>
                    </Layout>
                </Layout>
            );
        }
        
        // 渲染应用
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
    
    <!-- 页面组件定义 -->
    <script type="text/babel">
        // 仪表盘页面组件
        function DashboardPage({ systemData, loading, refreshData }) {
            const { Card, Row, Col, Statistic, Button, Space, Typography, Progress, Tag, Spin } = antd;
            const { Title, Text } = Typography;

            // 格式化货币
            const formatCurrency = (value, showSign = false) => {
                const formatted = new Intl.NumberFormat('zh-CN', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                }).format(Math.abs(value));

                if (showSign && value !== 0) {
                    return value > 0 ? `+${formatted}` : `-${formatted}`;
                }
                return formatted;
            };

            // 格式化百分比
            const formatPercentage = (value, showSign = true) => {
                const formatted = Math.abs(value).toFixed(2) + '%';
                if (showSign && value !== 0) {
                    return value > 0 ? `+${formatted}` : `-${formatted}`;
                }
                return formatted;
            };

            return React.createElement('div', null,
                // 页面头部
                React.createElement('div', { style: { marginBottom: 24 } },
                    React.createElement(Title, { level: 2, style: { margin: 0 } }, '交易仪表盘'),
                    React.createElement(Text, { type: 'secondary' }, '实时监控您的量化交易系统'),

                    React.createElement('div', { style: { marginTop: 16 } },
                        React.createElement(Space, null,
                            React.createElement(Button, {
                                icon: loading ? React.createElement(Spin, { size: 'small' }) : '🔄',
                                onClick: refreshData,
                                disabled: loading
                            }, '刷新数据'),
                            React.createElement(Tag, { color: systemData.positions.length > 0 ? 'green' : 'default' },
                                `${systemData.positions.length} 个持仓`
                            ),
                            React.createElement(Tag, { color: 'blue' }, 'AI分析运行中')
                        )
                    )
                ),

                // 核心指标卡片
                React.createElement(Row, { gutter: [24, 24], style: { marginBottom: 24 } },
                    // 账户资产
                    React.createElement(Col, { xs: 24, sm: 12, lg: 6 },
                        React.createElement(Card, null,
                            React.createElement(Statistic, {
                                title: '总资产',
                                value: systemData.totalBalance,
                                precision: 2,
                                suffix: 'USDT',
                                valueStyle: { color: '#1890ff' }
                            }),
                            React.createElement('div', { style: { marginTop: 8 } },
                                React.createElement(Text, { type: 'secondary' }, `可用: ${formatCurrency(systemData.availableBalance)}`)
                            ),
                            React.createElement('div', null,
                                React.createElement(Text, { type: 'secondary' }, `保证金: ${formatCurrency(systemData.usedMargin)}`)
                            )
                        )
                    ),

                    // 今日盈亏
                    React.createElement(Col, { xs: 24, sm: 12, lg: 6 },
                        React.createElement(Card, null,
                            React.createElement(Statistic, {
                                title: '今日盈亏',
                                value: systemData.todayPnl,
                                precision: 2,
                                suffix: 'USDT',
                                valueStyle: { color: systemData.todayPnl >= 0 ? '#52c41a' : '#ff4d4f' },
                                prefix: systemData.todayPnl >= 0 ? '📈' : '📉'
                            }),
                            React.createElement('div', { style: { marginTop: 8 } },
                                React.createElement(Text, {
                                    style: { color: systemData.todayPnlPercent >= 0 ? '#52c41a' : '#ff4d4f' }
                                }, formatPercentage(systemData.todayPnlPercent))
                            )
                        )
                    ),

                    // 胜率统计
                    React.createElement(Col, { xs: 24, sm: 12, lg: 6 },
                        React.createElement(Card, null,
                            React.createElement(Statistic, {
                                title: '胜率',
                                value: systemData.winRate,
                                suffix: '%',
                                valueStyle: { color: systemData.winRate >= 60 ? '#52c41a' : '#faad14' }
                            }),
                            React.createElement('div', { style: { marginTop: 8 } },
                                React.createElement(Progress, {
                                    percent: systemData.winRate,
                                    size: 'small',
                                    strokeColor: systemData.winRate >= 60 ? '#52c41a' : '#faad14',
                                    showInfo: false
                                })
                            ),
                            React.createElement('div', null,
                                React.createElement(Text, { type: 'secondary' }, `总交易: ${systemData.totalTrades} 笔`)
                            )
                        )
                    ),

                    // 平均盈利
                    React.createElement(Col, { xs: 24, sm: 12, lg: 6 },
                        React.createElement(Card, null,
                            React.createElement(Statistic, {
                                title: '平均盈利',
                                value: systemData.avgProfit,
                                precision: 2,
                                suffix: 'USDT',
                                valueStyle: { color: systemData.avgProfit >= 0 ? '#52c41a' : '#ff4d4f' }
                            }),
                            React.createElement('div', { style: { marginTop: 8 } },
                                React.createElement(Text, { type: 'secondary' }, '最大回撤: '),
                                React.createElement(Text, { style: { color: '#ff4d4f' } },
                                    formatPercentage(systemData.maxDrawdown)
                                )
                            )
                        )
                    )
                )
            );
        }

        // 交易控制页面组件
        function TradingPage({ systemData, isTrading, setIsTrading, callAPI }) {
            const { Card, Row, Col, Button, Tag, Space, Typography, Alert, Modal, message } = antd;
            const { Title, Text } = Typography;

            return React.createElement('div', null,
                React.createElement('div', { style: { marginBottom: 24 } },
                    React.createElement(Title, { level: 2, style: { margin: 0 } }, '交易控制'),
                    React.createElement(Text, { type: 'secondary' }, '启动、停止和监控交易系统')
                ),

                React.createElement(Alert, {
                    message: isTrading ? '交易系统运行中' : '交易系统已停止',
                    description: isTrading ? 'AI引擎正在监控市场并执行自动交易策略' : '选择交易对并启动系统开始自动交易',
                    type: isTrading ? 'success' : 'warning',
                    showIcon: true,
                    style: { marginBottom: 24 }
                }),

                React.createElement(Card, { title: '🎮 交易控制' },
                    React.createElement('div', { style: { textAlign: 'center', padding: '40px' } },
                        React.createElement(Button, {
                            size: 'large',
                            type: isTrading ? 'default' : 'primary',
                            danger: isTrading,
                            style: { width: '200px', height: '60px', fontSize: '18px' }
                        }, isTrading ? '⏹️ 停止交易' : '▶️ 启动交易')
                    )
                )
            );
        }

        // 持仓管理页面组件
        function PositionsPage({ systemData, callAPI }) {
            const { Card, Typography } = antd;
            const { Title, Text } = Typography;

            return React.createElement('div', null,
                React.createElement('div', { style: { marginBottom: 24 } },
                    React.createElement(Title, { level: 2, style: { margin: 0 } }, '持仓管理'),
                    React.createElement(Text, { type: 'secondary' }, '查看和管理当前持仓')
                ),

                React.createElement(Card, { title: '💼 持仓列表' },
                    React.createElement('div', { style: { textAlign: 'center', padding: '40px' } },
                        React.createElement(Text, { type: 'secondary' }, '暂无持仓数据')
                    )
                )
            );
        }

        // 系统配置页面组件
        function ConfigPage({ callAPI }) {
            const { Card, Typography } = antd;
            const { Title, Text } = Typography;

            return React.createElement('div', null,
                React.createElement('div', { style: { marginBottom: 24 } },
                    React.createElement(Title, { level: 2, style: { margin: 0 } }, '系统配置'),
                    React.createElement(Text, { type: 'secondary' }, '配置交易参数和系统设置')
                ),

                React.createElement(Card, { title: '⚙️ 配置参数' },
                    React.createElement('div', { style: { textAlign: 'center', padding: '40px' } },
                        React.createElement(Text, { type: 'secondary' }, '配置功能开发中...')
                    )
                )
            );
        }

        // 日志记录页面组件
        function LogsPage({ callAPI }) {
            const { Card, Typography } = antd;
            const { Title, Text } = Typography;

            return React.createElement('div', null,
                React.createElement('div', { style: { marginBottom: 24 } },
                    React.createElement(Title, { level: 2, style: { margin: 0 } }, '日志记录'),
                    React.createElement(Text, { type: 'secondary' }, '查看系统运行日志和任务状态')
                ),

                React.createElement(Card, { title: '📋 系统日志' },
                    React.createElement('div', { style: { textAlign: 'center', padding: '40px' } },
                        React.createElement(Text, { type: 'secondary' }, '日志功能开发中...')
                    )
                )
            );
        }
    </script>
</body>
</html>
